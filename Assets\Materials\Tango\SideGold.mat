%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-5424713252376756598
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 12
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SideGold
  m_Shader: {fileID: 4800000, guid: 6e4ae4064600d784cac1e41a9e6f2e59, type: 3}
  m_ValidKeywords:
  - _DISABLE_SSR_TRANSPARENT
  - _NORMALMAP
  - _NORMALMAP_TANGENT_SPACE
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2225
  stringTagMap: {}
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  - RayTracingPrepass
  - MOTIONVECTORS
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AnisotropyMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap:
        m_Texture: {fileID: 2800000, guid: bb2289b1fc483774c901782564c328ba, type: 3}
        m_Scale: {x: 0.1, y: 0.1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: bb2289b1fc483774c901782564c328ba, type: 3}
        m_Scale: {x: 0.1, y: 0.1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: 50ca665d35bf8bb4babcdb999f5a8132, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS:
        m_Texture: {fileID: 2800000, guid: 8ecbc929776319649a5e40c574137ad9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmittanceColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AORemapMax: 1
    - _AORemapMin: 0
    - _ATDistance: 1
    - _AddPrecomputedVelocity: 0
    - _AlbedoAffectEmissive: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaCutoffPostpass: 0.5
    - _AlphaCutoffPrepass: 0.5
    - _AlphaCutoffShadow: 0.5
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _Anisotropy: 0
    - _BlendMode: 0
    - _CoatMask: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DepthOffsetEnable: 0
    - _DetailAlbedoScale: 1
    - _DetailNormalScale: 1
    - _DetailSmoothnessScale: 1
    - _DiffusionProfile: 0
    - _DiffusionProfileHash: 0
    - _DisplacementLockObjectScale: 1
    - _DisplacementLockTilingScale: 1
    - _DisplacementMode: 0
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 1
    - _DstBlend: 0
    - _EmissiveColorMode: 1
    - _EmissiveExposureWeight: 1
    - _EmissiveIntensity: 1
    - _EmissiveIntensityUnit: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _EnableGeometricSpecularAA: 0
    - _EnergyConservingSpecularColor: 1
    - _HeightAmplitude: 0.02
    - _HeightCenter: 0.5
    - _HeightMapParametrization: 0
    - _HeightMax: 1
    - _HeightMin: -1
    - _HeightOffset: 0
    - _HeightPoMAmplitude: 2
    - _HeightTessAmplitude: 2
    - _HeightTessCenter: 0.5
    - _InvTilingScale: 10
    - _Ior: 1.5
    - _IridescenceMask: 1
    - _IridescenceThickness: 1
    - _LinkDetailsWithBase: 1
    - _MaterialID: 1
    - _Metallic: 1
    - _MetallicRemapMax: 1
    - _MetallicRemapMin: 0
    - _NormalMapSpace: 0
    - _NormalScale: 0.16
    - _OpaqueCullMode: 2
    - _PPDLodThreshold: 5
    - _PPDMaxSamples: 15
    - _PPDMinSamples: 5
    - _PPDPrimitiveLength: 1
    - _PPDPrimitiveWidth: 1
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _Smoothness: 0.645
    - _SmoothnessRemapMax: 1
    - _SmoothnessRemapMin: 0
    - _SpecularAAScreenSpaceVariance: 0.1
    - _SpecularAAThreshold: 0.2
    - _SpecularOcclusionMode: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SubsurfaceMask: 1
    - _SupportDecals: 1
    - _SurfaceType: 0
    - _TexWorldScale: 1
    - _TexWorldScaleEmissive: 1
    - _Thickness: 1
    - _TransmissionEnable: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UVBase: 0
    - _UVDetail: 0
    - _UVEmissive: 0
    - _UseEmissiveIntensity: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.8207547, g: 0.65126973, b: 0.058072258, a: 1}
    - _BaseColorMap_MipInfo: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 0.8207547, g: 0.65126973, b: 0.05807224, a: 1}
    - _DiffusionProfileAsset: {r: 0, g: 0, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColorLDR: {r: 0, g: 0, b: 0, a: 1}
    - _InvPrimScale: {r: 1, g: 1, b: 0, a: 0}
    - _IridescenceThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _ThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _TransmittanceColor: {r: 1, g: 1, b: 1, a: 1}
    - _UVDetailsMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMaskEmissive: {r: 1, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
