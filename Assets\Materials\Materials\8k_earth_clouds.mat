%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 8k_earth_clouds
  m_Shader: {fileID: 4800000, guid: c4edd00ff2db5b24391a4fcb1762e459, type: 3}
  m_ValidKeywords:
  - _SURFACE_TYPE_TRANSPARENT
  m_InvalidKeywords:
  - _MASKMAP
  - _NORMALMAP_TANGENT_SPACE
  - _NORMALMAP_TANGENT_SPACE0
  - _NORMALMAP_TANGENT_SPACE1
  - _NORMALMAP_TANGENT_SPACE2
  - _NORMALMAP_TANGENT_SPACE3
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
    RenderType: Transparent
  disabledShaderPasses:
  - MOTIONVECTORS
  - DistortionVectors
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AnisotropyMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap0:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap1:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortionVectorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LayerInfluenceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LayerMaskMap:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmittanceColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UnlitColorMap:
        m_Texture: {fileID: 2800000, guid: c388a9e3dd7e91143a31eb4790cbf44a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AORemapMax: 1
    - _AORemapMax0: 1
    - _AORemapMax1: 1
    - _AORemapMax2: 1
    - _AORemapMax3: 1
    - _AORemapMin: 0
    - _AORemapMin0: 0
    - _AORemapMin1: 0
    - _AORemapMin2: 0
    - _AORemapMin3: 0
    - _ATDistance: 1
    - _AddPrecomputedVelocity: 0
    - _AlbedoAffectEmissive: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaCutoffPostpass: 0.5
    - _AlphaCutoffPrepass: 0.5
    - _AlphaCutoffShadow: 0.5
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _Anisotropy: 0
    - _BlendMode: 0
    - _CoatMask: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DepthOffsetEnable: 0
    - _DetailAlbedoScale: 1
    - _DetailAlbedoScale0: 1
    - _DetailAlbedoScale1: 1
    - _DetailAlbedoScale2: 1
    - _DetailAlbedoScale3: 1
    - _DetailNormalScale: 1
    - _DetailNormalScale0: 1
    - _DetailNormalScale1: 1
    - _DetailNormalScale2: 1
    - _DetailNormalScale3: 1
    - _DetailSmoothnessScale: 1
    - _DetailSmoothnessScale0: 1
    - _DetailSmoothnessScale1: 1
    - _DetailSmoothnessScale2: 1
    - _DetailSmoothnessScale3: 1
    - _DiffusionProfile: 0
    - _DiffusionProfile0: 0
    - _DiffusionProfile1: 0
    - _DiffusionProfile2: 0
    - _DiffusionProfile3: 0
    - _DiffusionProfileHash: 0
    - _DiffusionProfileHash0: 0
    - _DiffusionProfileHash1: 0
    - _DiffusionProfileHash2: 0
    - _DiffusionProfileHash3: 0
    - _DisplacementLockObjectScale: 1
    - _DisplacementLockTilingScale: 1
    - _DisplacementMode: 0
    - _DistortionBlendMode: 0
    - _DistortionBlurBlendMode: 0
    - _DistortionBlurDstBlend: 1
    - _DistortionBlurRemapMax: 1
    - _DistortionBlurRemapMin: 0
    - _DistortionBlurScale: 1
    - _DistortionBlurSrcBlend: 1
    - _DistortionDepthTest: 1
    - _DistortionDstBlend: 1
    - _DistortionEnable: 0
    - _DistortionOnly: 0
    - _DistortionScale: 1
    - _DistortionSrcBlend: 1
    - _DistortionVectorBias: -1
    - _DistortionVectorScale: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 1
    - _DstBlend: 10
    - _EmissiveColorMode: 1
    - _EmissiveExposureWeight: 1
    - _EmissiveIntensity: 0
    - _EmissiveIntensityUnit: 0
    - _EnableBlendModePreserveSpecularLighting: 0
    - _EnableFogOnTransparent: 0
    - _EnableGeometricSpecularAA: 0
    - _EnergyConservingSpecularColor: 1
    - _HeightAmplitude: 0.02
    - _HeightAmplitude0: 0.02
    - _HeightAmplitude1: 0.02
    - _HeightAmplitude2: 0.02
    - _HeightAmplitude3: 0.02
    - _HeightCenter: 0.5
    - _HeightCenter0: 0.5
    - _HeightCenter1: 0.5
    - _HeightCenter2: 0.5
    - _HeightCenter3: 0.5
    - _HeightMapParametrization: 0
    - _HeightMapParametrization0: 0
    - _HeightMapParametrization1: 0
    - _HeightMapParametrization2: 0
    - _HeightMapParametrization3: 0
    - _HeightMax: 1
    - _HeightMax0: 1
    - _HeightMax1: 1
    - _HeightMax2: 1
    - _HeightMax3: 1
    - _HeightMin: -1
    - _HeightMin0: -1
    - _HeightMin1: -1
    - _HeightMin2: -1
    - _HeightMin3: -1
    - _HeightOffset: 0
    - _HeightOffset0: 0
    - _HeightOffset1: 0
    - _HeightOffset2: 0
    - _HeightOffset3: 0
    - _HeightPoMAmplitude: 2
    - _HeightPoMAmplitude0: 2
    - _HeightPoMAmplitude1: 2
    - _HeightPoMAmplitude2: 2
    - _HeightPoMAmplitude3: 2
    - _HeightTessAmplitude: 2
    - _HeightTessAmplitude0: 2
    - _HeightTessAmplitude1: 2
    - _HeightTessAmplitude2: 2
    - _HeightTessAmplitude3: 2
    - _HeightTessCenter: 0.5
    - _HeightTessCenter0: 0.5
    - _HeightTessCenter1: 0.5
    - _HeightTessCenter2: 0.5
    - _HeightTessCenter3: 0.5
    - _HeightTransition: 0
    - _IncludeIndirectLighting: 1
    - _InheritBaseColor1: 0
    - _InheritBaseColor2: 0
    - _InheritBaseColor3: 0
    - _InheritBaseHeight1: 0
    - _InheritBaseHeight2: 0
    - _InheritBaseHeight3: 0
    - _InheritBaseNormal1: 0
    - _InheritBaseNormal2: 0
    - _InheritBaseNormal3: 0
    - _InvTilingScale: 1
    - _InvTilingScale0: 1
    - _InvTilingScale1: 1
    - _InvTilingScale2: 1
    - _InvTilingScale3: 1
    - _Ior: 1
    - _IridescenceMask: 1
    - _IridescenceThickness: 1
    - _LayerCount: 2
    - _LinkDetailsWithBase: 1
    - _LinkDetailsWithBase0: 1
    - _LinkDetailsWithBase1: 1
    - _LinkDetailsWithBase2: 1
    - _LinkDetailsWithBase3: 1
    - _MaterialID: 1
    - _Metallic: 0.873
    - _Metallic0: 0
    - _Metallic1: 0
    - _Metallic2: 0
    - _Metallic3: 0
    - _MetallicRemapMax: 1
    - _MetallicRemapMax0: 1
    - _MetallicRemapMax1: 1
    - _MetallicRemapMax2: 1
    - _MetallicRemapMax3: 1
    - _MetallicRemapMin: 0.98265994
    - _MetallicRemapMin0: 0
    - _MetallicRemapMin1: 0
    - _MetallicRemapMin2: 0
    - _MetallicRemapMin3: 0
    - _NormalMapSpace: 0
    - _NormalMapSpace0: 0
    - _NormalMapSpace1: 0
    - _NormalMapSpace2: 0
    - _NormalMapSpace3: 0
    - _NormalScale: 0
    - _NormalScale0: 1
    - _NormalScale1: 1
    - _NormalScale2: 1
    - _NormalScale3: 1
    - _ObjectScaleAffectTile: 0
    - _OpacityAsDensity0: 0
    - _OpacityAsDensity1: 0
    - _OpacityAsDensity2: 0
    - _OpacityAsDensity3: 0
    - _OpaqueCullMode: 2
    - _PPDLodThreshold: 5
    - _PPDMaxSamples: 15
    - _PPDMinSamples: 5
    - _PPDPrimitiveLength: 1
    - _PPDPrimitiveWidth: 1
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 1
    - _RefractionModel: 0
    - _Smoothness: 0.609
    - _Smoothness0: 1
    - _Smoothness1: 1
    - _Smoothness2: 0.5
    - _Smoothness3: 0.5
    - _SmoothnessRemapMax: 0.020808041
    - _SmoothnessRemapMax0: 1
    - _SmoothnessRemapMax1: 1
    - _SmoothnessRemapMax2: 1
    - _SmoothnessRemapMax3: 1
    - _SmoothnessRemapMin: 0
    - _SmoothnessRemapMin0: 0
    - _SmoothnessRemapMin1: 0
    - _SmoothnessRemapMin2: 0
    - _SmoothnessRemapMin3: 0
    - _SpecularAAScreenSpaceVariance: 0.1
    - _SpecularAAThreshold: 0.2
    - _SpecularOcclusionMode: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 42
    - _SubsurfaceMask: 1
    - _SubsurfaceMask0: 1
    - _SubsurfaceMask1: 1
    - _SubsurfaceMask2: 1
    - _SubsurfaceMask3: 1
    - _SupportDecals: 1
    - _SurfaceType: 1
    - _TexWorldScale: 1
    - _TexWorldScale0: 1
    - _TexWorldScale1: 1
    - _TexWorldScale2: 1
    - _TexWorldScale3: 1
    - _TexWorldScaleBlendMask: 1
    - _TexWorldScaleEmissive: 1
    - _Thickness: 1
    - _Thickness0: 1
    - _Thickness1: 1
    - _Thickness2: 1
    - _Thickness3: 1
    - _TransmissionEnable: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UVBase: 0
    - _UVBase0: 0
    - _UVBase1: 0
    - _UVBase2: 0
    - _UVBase3: 0
    - _UVBlendMask: 0
    - _UVDetail: 0
    - _UVDetail0: 0
    - _UVDetail1: 0
    - _UVDetail2: 0
    - _UVDetail3: 0
    - _UVEmissive: 0
    - _UseDensityMode: 0
    - _UseEmissiveIntensity: 0
    - _UseHeightBasedBlend: 0
    - _UseMainLayerInfluence: 0
    - _UseShadowThreshold: 0
    - _VertexColorMode: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 4
    - _ZTestModeDistortion: 4
    - _ZTestTransparent: 4
    - _ZWrite: 0
    m_Colors:
    - _BaseColor: {r: 0.49056602, g: 0.49056602, b: 0.49056602, a: 1}
    - _BaseColor0: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColor1: {r: 0.03773582, g: 0.03773582, b: 0.03773582, a: 1}
    - _BaseColor2: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColor3: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColorMap0_MipInfo: {r: 0, g: 0, b: 0, a: 0}
    - _BaseColorMap_MipInfo: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DiffusionProfileAsset: {r: 0, g: 0, b: 0, a: 0}
    - _DiffusionProfileAsset0: {r: 0, g: 0, b: 0, a: 0}
    - _DiffusionProfileAsset1: {r: 0, g: 0, b: 0, a: 0}
    - _DiffusionProfileAsset2: {r: 0, g: 0, b: 0, a: 0}
    - _DiffusionProfileAsset3: {r: 0, g: 0, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 0}
    - _EmissiveColorLDR: {r: 1, g: 1, b: 1, a: 1}
    - _InvPrimScale: {r: 1, g: 1, b: 0, a: 0}
    - _IridescenceThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _ThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _ThicknessRemap0: {r: 0, g: 1, b: 0, a: 0}
    - _ThicknessRemap1: {r: 0, g: 1, b: 0, a: 0}
    - _ThicknessRemap2: {r: 0, g: 1, b: 0, a: 0}
    - _ThicknessRemap3: {r: 0, g: 1, b: 0, a: 0}
    - _TransmittanceColor: {r: 0.7773585, g: 0.8996517, b: 1, a: 1}
    - _UVDetailsMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVDetailsMappingMask0: {r: 1, g: 0, b: 0, a: 0}
    - _UVDetailsMappingMask1: {r: 1, g: 0, b: 0, a: 0}
    - _UVDetailsMappingMask2: {r: 1, g: 0, b: 0, a: 0}
    - _UVDetailsMappingMask3: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask0: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask1: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask2: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask3: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMaskBlendMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMaskEmissive: {r: 1, g: 0, b: 0, a: 0}
    - _UnlitColor: {r: 1, g: 1, b: 1, a: 1}
    - _UnlitColorMap_MipInfo: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
--- !u!114 &4012925770575503899
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 12
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
