%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7089757308646879465
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bcf384b154398e341b6b29969c078198, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 0
  quality:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.5
  maximumVelocity:
    m_OverrideState: 0
    m_Value: 200
  minimumVelocity:
    m_OverrideState: 0
    m_Value: 2
  cameraMotionBlur:
    m_OverrideState: 0
    m_Value: 1
  specialCameraClampMode:
    m_OverrideState: 0
    m_Value: 0
  cameraVelocityClamp:
    m_OverrideState: 0
    m_Value: 0.05
  cameraTranslationVelocityClamp:
    m_OverrideState: 0
    m_Value: 0.05
  cameraRotationVelocityClamp:
    m_OverrideState: 0
    m_Value: 0.03
  depthComparisonExtent:
    m_OverrideState: 0
    m_Value: 1
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 8
--- !u!114 &-1016694868962581565
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56b145d2b9ee1ac4f846968484e7485a, type: 3}
  m_Name: ContactShadows
  m_EditorClassIdentifier: 
  active: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  enable:
    m_OverrideState: 1
    m_Value: 1
  length:
    m_OverrideState: 1
    m_Value: 0.179
  opacity:
    m_OverrideState: 1
    m_Value: 1
  distanceScaleFactor:
    m_OverrideState: 1
    m_Value: 0.5
  maxDistance:
    m_OverrideState: 1
    m_Value: 50
  minDistance:
    m_OverrideState: 1
    m_Value: 0
  fadeDistance:
    m_OverrideState: 1
    m_Value: 5
  fadeInDistance:
    m_OverrideState: 1
    m_Value: 0
  rayBias:
    m_OverrideState: 1
    m_Value: 0.2
  thicknessScale:
    m_OverrideState: 1
    m_Value: 0.15
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 32
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DefaultSettingsVolumeProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 7686318427622180703}
  - {fileID: -1016694868962581565}
  - {fileID: 7502528774814404555}
  - {fileID: 7542669330009093999}
  - {fileID: 1501199423866068322}
  - {fileID: 5315503232242033309}
  - {fileID: 1932259527246508038}
  - {fileID: 448115243408767295}
  - {fileID: -7089757308646879465}
--- !u!114 &448115243408767295
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59b6606ef2548734bb6d11b9d160bc7e, type: 3}
  m_Name: HDRISky
  m_EditorClassIdentifier: 
  active: 0
  rotation:
    m_OverrideState: 0
    m_Value: 0
  skyIntensityMode:
    m_OverrideState: 0
    m_Value: 0
  exposure:
    m_OverrideState: 1
    m_Value: 11
  multiplier:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxValue:
    m_OverrideState: 1
    m_Value: 0.4660715
  upperHemisphereLuxColor:
    m_OverrideState: 1
    m_Value: {x: 0.18750614, y: 0.29181972, z: 0.5}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  hdriSky:
    m_OverrideState: 1
    m_Value: {fileID: 8900000, guid: 8253d41e6e8b11a4cbe77a4f8f82934d, type: 3}
  distortionMode:
    m_OverrideState: 0
    m_Value: 0
  flowmap:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  upperHemisphereOnly:
    m_OverrideState: 0
    m_Value: 1
  scrollOrientation:
    m_OverrideState: 0
    m_Value:
      mode: 1
      customValue: 0
      additiveValue: 0
      multiplyValue: 1
  scrollSpeed:
    m_OverrideState: 0
    m_Value:
      mode: 1
      customValue: 100
      additiveValue: 0
      multiplyValue: 1
  enableBackplate:
    m_OverrideState: 0
    m_Value: 0
  backplateType:
    m_OverrideState: 0
    m_Value: 0
  groundLevel:
    m_OverrideState: 0
    m_Value: 0
  scale:
    m_OverrideState: 0
    m_Value: {x: 32, y: 32}
  projectionDistance:
    m_OverrideState: 0
    m_Value: 16
  plateRotation:
    m_OverrideState: 0
    m_Value: 0
  plateTexRotation:
    m_OverrideState: 0
    m_Value: 0
  plateTexOffset:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0}
  blendAmount:
    m_OverrideState: 0
    m_Value: 0
  shadowTint:
    m_OverrideState: 0
    m_Value: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  pointLightShadow:
    m_OverrideState: 0
    m_Value: 0
  dirLightShadow:
    m_OverrideState: 0
    m_Value: 0
  rectLightShadow:
    m_OverrideState: 0
    m_Value: 0
  m_SkyVersion: 1
  enableDistortion:
    m_OverrideState: 0
    m_Value: 0
  procedural:
    m_OverrideState: 0
    m_Value: 1
  scrollDirection:
    m_OverrideState: 0
    m_Value: 0
  m_ObsoleteScrollSpeed:
    m_OverrideState: 0
    m_Value: 2
--- !u!114 &1501199423866068322
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  threshold:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 0
    m_Value: 0.138
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 1
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1932259527246508038
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: VisualEnvironment
  m_EditorClassIdentifier: 
  active: 0
  skyType:
    m_OverrideState: 1
    m_Value: 1
  cloudType:
    m_OverrideState: 0
    m_Value: 0
  skyAmbientMode:
    m_OverrideState: 0
    m_Value: 0
  windOrientation:
    m_OverrideState: 0
    m_Value: 0
  windSpeed:
    m_OverrideState: 0
    m_Value: 100
  fogType:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &5315503232242033309
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 0
  mode:
    m_OverrideState: 1
    m_Value: 1
  meteringMode:
    m_OverrideState: 0
    m_Value: 0
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 8.109213
  compensation:
    m_OverrideState: 1
    m_Value: 0
  limitMin:
    m_OverrideState: 1
    m_Value: 8.182661
  limitMax:
    m_OverrideState: 1
    m_Value: 15
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -12
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 18
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 22
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 1
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 0
    m_Value: {x: 40, y: 90}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 0
  targetMidGray:
    m_OverrideState: 0
    m_Value: 0
  centerAroundExposureTarget:
    m_OverrideState: 0
    m_Value: 0
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  proceduralRadii:
    m_OverrideState: 0
    m_Value: {x: 0.3, y: 0.3}
  maskMinIntensity:
    m_OverrideState: 0
    m_Value: -30
  maskMaxIntensity:
    m_OverrideState: 0
    m_Value: 30
  proceduralSoftness:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &7502528774814404555
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 1.4
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0
  radius:
    m_OverrideState: 1
    m_Value: 0.38
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  specularOcclusion:
    m_OverrideState: 0
    m_Value: 0.5
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  m_StepCount:
    m_OverrideState: 0
    m_Value: 6
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 0
    m_Value: 40
  m_BilateralUpsample:
    m_OverrideState: 0
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 0
    m_Value: 2
  m_RayLength:
    m_OverrideState: 0
    m_Value: 3
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 2
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &7542669330009093999
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
  gamma:
    m_OverrideState: 0
    m_Value: 1
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &7686318427622180703
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 0
  interCascadeBorders: 1
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 10000
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
  cascadeShadowSplitCount:
    m_OverrideState: 0
    m_Value: 4
  cascadeShadowSplit0:
    m_OverrideState: 1
    m_Value: 0.0683
  cascadeShadowSplit1:
    m_OverrideState: 1
    m_Value: 0.1756
  cascadeShadowSplit2:
    m_OverrideState: 0
    m_Value: 0.3
  cascadeShadowBorder0:
    m_OverrideState: 1
    m_Value: 0.19563913
  cascadeShadowBorder1:
    m_OverrideState: 1
    m_Value: 0.33990335
  cascadeShadowBorder2:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
