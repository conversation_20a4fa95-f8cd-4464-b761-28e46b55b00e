{"dependencies": {"com.justinpbarnett.unity-mcp": {"version": "https://github.com/justinpbarnett/unity-mcp.git?path=/UnityMcpBridge", "depth": 0, "source": "git", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.0.2"}, "hash": "eabf727894cb285cb1fd3084cb6444959eb19e66"}, "com.unity.burst": {"version": "1.8.11", "depth": 1, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.2.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "1.2.4", "depth": 1, "source": "registry", "dependencies": {"com.unity.burst": "1.6.6", "com.unity.test-framework": "1.1.31"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "1.0.6", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ide.cursor": {"version": "https://github.com/boxqkrtm/com.unity.ide.cursor.git", "depth": 0, "source": "git", "dependencies": {"com.unity.test-framework": "1.1.9"}, "hash": "5eab27e315296530f9c66ff7795273394a6464b6"}, "com.unity.ide.rider": {"version": "3.0.27", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.22", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.ide.vscode": {"version": "1.2.5", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.2.6", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.memoryprofiler": {"version": "0.7.1-preview.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.editorcoroutines": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.perception": {"version": "1.0.0-preview.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.burst": "1.7.3", "com.unity.collections": "1.2.4", "com.unity.nuget.newtonsoft-json": "3.0.2", "com.unity.render-pipelines.core": "12.1.7"}, "url": "https://packages.unity.com"}, "com.unity.probuilder": {"version": "5.2.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.imgui": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "12.1.13", "depth": 1, "source": "builtin", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.render-pipelines.high-definition": {"version": "12.1.13", "depth": 0, "source": "builtin", "dependencies": {"com.unity.mathematics": "1.2.4", "com.unity.burst": "1.8.9", "com.unity.modules.video": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.render-pipelines.core": "12.1.13", "com.unity.shadergraph": "12.1.13", "com.unity.visualeffectgraph": "12.1.13", "com.unity.render-pipelines.high-definition-config": "12.1.13"}}, "com.unity.render-pipelines.high-definition-config": {"version": "12.1.13", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "12.1.13"}}, "com.unity.searcher": {"version": "4.9.1", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "1.0.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "12.1.13", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "12.1.13", "com.unity.searcher": "4.9.1"}}, "com.unity.test-framework": {"version": "1.1.33", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.textmeshpro": {"version": "3.0.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.6.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.ui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.ui.builder": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.visualeffectgraph": {"version": "12.1.13", "depth": 1, "source": "builtin", "dependencies": {"com.unity.shadergraph": "12.1.13", "com.unity.render-pipelines.core": "12.1.13"}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.uielementsnative": "1.0.0"}}, "com.unity.modules.uielementsnative": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}