%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 781cc897cf8675041a751163b51f97dd, type: 3}
  m_Name: HDRenderPipelineGlobalSettings
  m_EditorClassIdentifier: 
  m_DefaultVolumeProfile: {fileID: 11400000, guid: da4a02c9a7345fd4fb9a68c8672eff57, type: 2}
  m_LookDevVolumeProfile: {fileID: 11400000, guid: b7bb4c9497120cb4dae8b8a829374269, type: 2}
  m_RenderingPathDefaultCameraFrameSettings:
    bitDatas:
      data1: 72198260625768269
      data2: 13763000477350297624
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 2
    sssCustomSampleBudget: 20
    msaaMode: 1
    materialQuality: 0
  m_RenderingPathDefaultBakedOrCustomReflectionFrameSettings:
    bitDatas:
      data1: 135310754214733
      data2: 4539628428684427288
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    msaaMode: 1
    materialQuality: 0
  m_RenderingPathDefaultRealtimeReflectionFrameSettings:
    bitDatas:
      data1: 139923391782733
      data2: 13763000465807638544
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    msaaMode: 1
    materialQuality: 0
  m_RenderPipelineResources: {fileID: 11400000, guid: 3ce144cff5783da45aa5d4fdc2da14b7, type: 2}
  m_RenderPipelineRayTracingResources: {fileID: 0}
  beforeTransparentCustomPostProcesses: []
  beforePostProcessCustomPostProcesses: []
  afterPostProcessBlursCustomPostProcesses: []
  afterPostProcessCustomPostProcesses: []
  beforeTAACustomPostProcesses: []
  lightLayerName0: Light Layer default
  lightLayerName1: Light Layer 1
  lightLayerName2: Light Layer 2
  lightLayerName3: Light Layer 3
  lightLayerName4: Light Layer 4
  lightLayerName5: Light Layer 5
  lightLayerName6: Light Layer 6
  lightLayerName7: Light Layer 7
  decalLayerName0: Decal Layer default
  decalLayerName1: Decal Layer 1
  decalLayerName2: Decal Layer 2
  decalLayerName3: Decal Layer 3
  decalLayerName4: Decal Layer 4
  decalLayerName5: Decal Layer 5
  decalLayerName6: Decal Layer 6
  decalLayerName7: Decal Layer 7
  shaderVariantLogLevel: 0
  lensAttenuationMode: 0
  diffusionProfileSettingsList:
  - {fileID: 11400000, guid: c9881e4f31a8c544688090c1e2243a32, type: 2}
  - {fileID: 11400000, guid: 10b02c39e611b424cbb410b066a9e62f, type: 2}
  - {fileID: 11400000, guid: d8f2103ec57c4e04c8c48d4c91f26e67, type: 2}
  rendererListCulling: 0
  DLSSProjectId: 000000
  useDLSSCustomProjectId: 0
  supportProbeVolumes: 0
  supportRuntimeDebugDisplay: 0
  apvScenesData:
    serializedBounds: []
    serializedHasVolumes: []
    serializedProfiles: []
    serializedBakeSettings: []
    serializedBakingSets: []
  m_Version: 3
