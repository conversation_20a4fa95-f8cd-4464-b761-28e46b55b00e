%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DefaultLookDevProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 8761387877531654226}
  - {fileID: 1902828633788537306}
  - {fileID: 1880163708194025631}
  - {fileID: 2340290907100754200}
--- !u!114 &1880163708194025631
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 0
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 4
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  radius:
    m_OverrideState: 1
    m_Value: 1
    min: 0.25
    max: 5
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
    min: 0
    max: 1
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
    min: 0
    max: 1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  m_StepCount:
    m_OverrideState: 0
    m_Value: 6
    min: 2
    max: 32
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 0
    m_Value: 40
    min: 16
    max: 256
  m_BilateralUpsample:
    m_OverrideState: 0
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 0
    m_Value: 2
    min: 1
    max: 6
  m_RayLength:
    m_OverrideState: 0
    m_Value: 3
    min: 0
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 2
    min: 1
    max: 64
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0.001
    max: 1
--- !u!114 &1902828633788537306
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 0
  m_AdvancedMode: 0
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 25000
    min: 0
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  cascadeShadowSplitCount:
    m_OverrideState: 1
    m_Value: 2
    min: 1
    max: 4
  cascadeShadowSplit0:
    m_OverrideState: 0
    m_Value: 0.05
  cascadeShadowSplit1:
    m_OverrideState: 0
    m_Value: 0.15
  cascadeShadowSplit2:
    m_OverrideState: 0
    m_Value: 0.3
  cascadeShadowBorder0:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder1:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder2:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &2340290907100754200
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 0
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 3
  threshold:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.1
    min: 0
    max: 1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
    min: 0
    max: 1
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  anamorphic:
    m_OverrideState: 0
    m_Value: 1
  m_Resolution:
    m_OverrideState: 0
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 0
    m_Value: 0
  m_HighQualityFiltering:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &8761387877531654226
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 0
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 1
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  gamma:
    m_OverrideState: 0
    m_Value: 1
    min: 0.001
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
