using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ClassificationAnnotationManager : MonoBehaviour
{
    public string outputPath = "Assets/Datasets/SpacecraftClassification/";
    public string[] spacecraftClasses = {"ISS", "Dragon", "Soyuz", "Progress", "Cygnus", "HTV"};
    
    public void GenerateAnnotation(string filename, string spacecraftClass)
    {
        // 生成精细化分类标注的核心逻辑
        Debug.Log("Generating classification annotation for: " + spacecraftClass);
    }
}