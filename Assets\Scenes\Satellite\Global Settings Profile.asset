%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8880180986593477596
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b24b200358312b4fa1004e2431c2f1f, type: 3}
  m_Name: ShadowsMidtonesHighlights
  m_EditorClassIdentifier: 
  active: 0
  shadows:
    m_OverrideState: 1
    m_Value: {x: 1, y: 0.9561431, z: 0.95267326, w: 0}
  midtones:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  highlights:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  shadowsStart:
    m_OverrideState: 0
    m_Value: 0
  shadowsEnd:
    m_OverrideState: 0
    m_Value: 0.3
  highlightsStart:
    m_OverrideState: 0
    m_Value: 0.55
  highlightsEnd:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &-4309246182469139328
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b8bcdf71d7fafa419fca1ed162f5fc9, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 0
  postExposure:
    m_OverrideState: 0
    m_Value: 0
  contrast:
    m_OverrideState: 0
    m_Value: -9
  colorFilter:
    m_OverrideState: 0
    m_Value: {r: 0.4716981, g: 0.4716981, b: 0.4716981, a: 1}
  hueShift:
    m_OverrideState: 1
    m_Value: 6
  saturation:
    m_OverrideState: 1
    m_Value: -100
--- !u!114 &-3815843167143749930
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: DepthExample
  m_EditorClassIdentifier: Assembly-CSharp::DepthExample
  active: 0
  m_AdvancedMode: 0
  depthDistance:
    m_OverrideState: 1
    m_Value: 0.8
    min: 0
    max: 32
  enabled:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
--- !u!114 &-3793586848727318875
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a6b00fcf518bb94a90b408492e07b44, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  type:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
  response:
    m_OverrideState: 1
    m_Value: 0
  texture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
--- !u!114 &-3747141256491400313
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 1
  meteringMode:
    m_OverrideState: 1
    m_Value: 0
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 9
  compensation:
    m_OverrideState: 1
    m_Value: 1
  limitMin:
    m_OverrideState: 1
    m_Value: 9.280577
  limitMax:
    m_OverrideState: 1
    m_Value: 15
  curveMap:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 2.6533523
        value: -1.9558325
        inSlope: 0.49563435
        outSlope: 0.49563435
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 18.618744
        value: -10.000001
        inSlope: 0.045648143
        outSlope: 0.045648143
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.25833333
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMinCurveMap:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -81.282524
        value: 6.607132
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 41.641266
        value: 7.1781006
        inSlope: 2
        outSlope: 2
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  limitMaxCurveMap:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -8
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 12.955597
        value: 0.2772827
        inSlope: 1
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 1
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 1
    m_Value: 100
  adaptationSpeedLightToDark:
    m_OverrideState: 1
    m_Value: 100
  weightTextureMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  histogramPercentages:
    m_OverrideState: 1
    m_Value: {x: 17.531044, y: 53.250553}
  histogramUseCurveRemapping:
    m_OverrideState: 0
    m_Value: 1
  targetMidGray:
    m_OverrideState: 0
    m_Value: 2
  centerAroundExposureTarget:
    m_OverrideState: 1
    m_Value: 1
  proceduralCenter:
    m_OverrideState: 0
    m_Value: {x: -0.129896, y: -0.069519}
  proceduralRadii:
    m_OverrideState: 1
    m_Value: {x: 0.3, y: 0.3}
  maskMinIntensity:
    m_OverrideState: 1
    m_Value: 15
  maskMaxIntensity:
    m_OverrideState: 1
    m_Value: 50
  proceduralSoftness:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-3424370619814116239
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32b6af8f7ad32324cb6941c3290e5895, type: 3}
  m_Name: MicroShadowing
  m_EditorClassIdentifier: 
  active: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  opacity:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-52356056622153534
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  rayTracing:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 1.72
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 1
  radius:
    m_OverrideState: 1
    m_Value: 1.84
  spatialBilateralAggressiveness:
    m_OverrideState: 1
    m_Value: 0.388
  temporalAccumulation:
    m_OverrideState: 1
    m_Value: 0
  ghostingReduction:
    m_OverrideState: 1
    m_Value: 0.681
  blurSharpness:
    m_OverrideState: 0
    m_Value: 1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  specularOcclusion:
    m_OverrideState: 0
    m_Value: 0.5
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  m_StepCount:
    m_OverrideState: 1
    m_Value: 16
  m_FullResolution:
    m_OverrideState: 1
    m_Value: 1
  m_MaximumRadiusInPixels:
    m_OverrideState: 1
    m_Value: 80
  m_BilateralUpsample:
    m_OverrideState: 1
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 1
    m_Value: 4
  m_RayLength:
    m_OverrideState: 1
    m_Value: 20
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 8
  m_Denoise:
    m_OverrideState: 1
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 1
    m_Value: 0.65
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: Global Settings Profile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 4583595497993739358}
  - {fileID: -3793586848727318875}
  - {fileID: -4309246182469139328}
  - {fileID: -3424370619814116239}
  - {fileID: -3747141256491400313}
  - {fileID: -52356056622153534}
  - {fileID: 4830945269076349088}
  - {fileID: 8282343615474588102}
  - {fileID: -8880180986593477596}
  - {fileID: 151746934957172005}
  - {fileID: 4142872503974715127}
  - {fileID: 1489261638050419628}
--- !u!114 &151746934957172005
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c1bfcd0f0fa7b8468f281d6bbbaf320, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 0
  intensity:
    m_OverrideState: 0
    m_Value: 0.09
  xMultiplier:
    m_OverrideState: 0
    m_Value: 1
  yMultiplier:
    m_OverrideState: 0
    m_Value: 1
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  scale:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &1489261638050419628
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d7593b3a9277ac4696b20006c21dde2, type: 3}
  m_Name: VisualEnvironment
  m_EditorClassIdentifier: 
  active: 0
  skyType:
    m_OverrideState: 1
    m_Value: 1
  cloudType:
    m_OverrideState: 0
    m_Value: 0
  skyAmbientMode:
    m_OverrideState: 1
    m_Value: 0
  windOrientation:
    m_OverrideState: 0
    m_Value: 0
  windSpeed:
    m_OverrideState: 0
    m_Value: 100
  fogType:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &4142872503974715127
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a81bcacc415a1f743bfdf703afc52027, type: 3}
  m_Name: GradientSky
  m_EditorClassIdentifier: 
  active: 0
  rotation:
    m_OverrideState: 0
    m_Value: 0
  skyIntensityMode:
    m_OverrideState: 0
    m_Value: 0
  exposure:
    m_OverrideState: 0
    m_Value: 0
  multiplier:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxValue:
    m_OverrideState: 0
    m_Value: 1
  upperHemisphereLuxColor:
    m_OverrideState: 0
    m_Value: {x: 0, y: 0, z: 0}
  desiredLuxValue:
    m_OverrideState: 0
    m_Value: 20000
  updateMode:
    m_OverrideState: 0
    m_Value: 0
  updatePeriod:
    m_OverrideState: 0
    m_Value: 0
  includeSunInBaking:
    m_OverrideState: 0
    m_Value: 0
  top:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 1, a: 1}
  middle:
    m_OverrideState: 1
    m_Value: {r: 0.3, g: 0.7, b: 1, a: 1}
  bottom:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  gradientDiffusion:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &4583595497993739358
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 2
  threshold:
    m_OverrideState: 1
    m_Value: 2.38
  intensity:
    m_OverrideState: 1
    m_Value: 0.743
  scatter:
    m_OverrideState: 1
    m_Value: 0.936
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 0.6950914, b: 0, a: 1}
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 2800000, guid: 203b35c8eaa5f684c9d722891603d7cf, type: 3}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 1.36
  anamorphic:
    m_OverrideState: 1
    m_Value: 0
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityPrefiltering:
    m_OverrideState: 1
    m_Value: 1
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &4830945269076349088
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaa3b8214f75b354e9ba2caadd022259, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 1
    m_Value: 3
  focusMode:
    m_OverrideState: 1
    m_Value: 1
  focusDistance:
    m_OverrideState: 1
    m_Value: 6
  focusDistanceMode:
    m_OverrideState: 0
    m_Value: 0
  nearFocusStart:
    m_OverrideState: 1
    m_Value: 0
  nearFocusEnd:
    m_OverrideState: 1
    m_Value: 2.34
  farFocusStart:
    m_OverrideState: 1
    m_Value: 2.67
  farFocusEnd:
    m_OverrideState: 1
    m_Value: 3.5
  m_NearSampleCount:
    m_OverrideState: 1
    m_Value: 8
  m_NearMaxBlur:
    m_OverrideState: 1
    m_Value: 8
  m_FarSampleCount:
    m_OverrideState: 1
    m_Value: 16
  m_FarMaxBlur:
    m_OverrideState: 1
    m_Value: 7.99
  m_Resolution:
    m_OverrideState: 1
    m_Value: 1
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
  m_PhysicallyBased:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &8282343615474588102
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff652df78c97c4f963064ad1f34619, type: 3}
  m_Name: ColorCurves
  m_EditorClassIdentifier: 
  active: 0
  master:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  red:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  green:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  blue:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsHue:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsSat:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  satVsSat:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  lumVsSat:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  m_SelectedCurve: 0
